import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../services/cycle_day_calculator.dart';
import 'package:intl/intl.dart';

class MenstrualCycleTracker extends StatefulWidget {
  final int cycleDays;
  final int periodDays;
  final int currentDay;
  final int ovulationDays;
  final int ovulationDayStart;
  final double size;
  final TextStyle? textStyle;
  final Color outerCircleColor;
  final Color periodArcColor;
  final Color ovulationArcColor;
  final Color innerCircleColor;
  final Color pointerColor;
  final CycleDayInfo? cycleInfo;
  final DateTime? selectedDate;

  const MenstrualCycleTracker({
    required this.cycleDays,
    required this.periodDays,
    required this.currentDay,
    required this.ovulationDays,
    required this.ovulationDayStart,
    this.size = 300, // default size if not provided
    this.textStyle, // optional custom text style for numbers
    this.outerCircleColor = Colors.grey,
    this.periodArcColor = Colors.purple,
    this.ovulationArcColor = Colors.yellow, // Color for ovulation arc
    this.innerCircleColor = Colors.purple,
    this.pointerColor = Colors.white,
    this.cycleInfo,
    this.selectedDate,
    Key? key,
  }) : super(key: key);

  // New constructor that uses CycleDayInfo
  MenstrualCycleTracker.fromCycleInfo({
    required CycleDayInfo cycleInfo,
    this.size = 300,
    this.textStyle,
    this.outerCircleColor = Colors.grey,
    this.periodArcColor = Colors.purple,
    this.ovulationArcColor = Colors.yellow,
    this.innerCircleColor = Colors.purple,
    this.pointerColor = Colors.white,
    this.selectedDate,
    Key? key,
  })  : cycleDays = cycleInfo.cycleLength,
        periodDays = _calculatePeriodLength(cycleInfo),
        currentDay = cycleInfo.cycleDay,
        ovulationDays = _calculateOvulationDays(cycleInfo),
        ovulationDayStart = _calculateOvulationStart(cycleInfo),
        cycleInfo = cycleInfo,
        super(key: key);

  // Helper method to calculate period length from cycle info
  static int _calculatePeriodLength(CycleDayInfo cycleInfo) {
    // Use the actual calculated period length from cycle info
    return cycleInfo.periodLength;
  }

  // Helper method to calculate ovulation days from actual data
  static int _calculateOvulationDays(CycleDayInfo cycleInfo) {
    if (cycleInfo.ovulationDates.isNotEmpty) {
      // Calculate the span of actual ovulation dates
      final sortedDates = cycleInfo.ovulationDates.toList()..sort();
      if (sortedDates.length == 1) {
        return 1; // Single ovulation day
      } else {
        // Calculate the span from first to last ovulation date
        final daySpan =
            sortedDates.last.difference(sortedDates.first).inDays + 1;
        return daySpan.clamp(1, 7); // Max 7 days for safety
      }
    } else {
      // Default to 5-day fertile window if no actual data
      return 5;
    }
  }

  // Helper method to calculate ovulation start day
  static int _calculateOvulationStart(CycleDayInfo cycleInfo) {
    if (cycleInfo.ovulationDates.isNotEmpty) {
      // Use the earliest ovulation date from actual data
      final sortedDates = cycleInfo.ovulationDates.toList()..sort();
      final earliestOvulationDate = sortedDates.first;
      final daysSinceStart =
          earliestOvulationDate.difference(cycleInfo.cycleStartDate).inDays + 1;
      return daysSinceStart.clamp(1, cycleInfo.cycleLength);
    } else {
      // Calculate ovulation as approximately 14 days before next cycle
      // For a 28-day cycle, this would be day 12 (to center the 5-day window around day 14)
      return (cycleInfo.cycleLength - 16).clamp(1, cycleInfo.cycleLength);
    }
  }

  @override
  State<MenstrualCycleTracker> createState() => _MenstrualCycleTrackerState();
}

class _MenstrualCycleTrackerState extends State<MenstrualCycleTracker> {
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(widget.size, widget.size),
      painter: CycleTrackerPainter(
        cycleDays: widget.cycleDays,
        periodDays: widget.periodDays,
        currentDay: widget.currentDay,
        ovulationDays: widget.ovulationDays,
        ovulationDayStart: widget.ovulationDayStart,
        textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
        outerCircleColor: widget.outerCircleColor,
        periodArcColor: widget.periodArcColor,
        ovulationArcColor: widget.ovulationArcColor,
        innerCircleColor: widget.innerCircleColor,
        pointerColor: widget.pointerColor,
        cycleInfo: widget.cycleInfo,
        selectedDate: widget.selectedDate,
      ),
    );
  }
}

class CycleTrackerPainter extends CustomPainter {
  final int cycleDays;
  final int periodDays;
  final int currentDay;
  final int ovulationDays;
  final int ovulationDayStart;
  final TextStyle textStyle;
  final Color outerCircleColor;
  final Color periodArcColor;
  final Color ovulationArcColor;
  final Color innerCircleColor;
  final Color pointerColor;
  final CycleDayInfo? cycleInfo;
  final DateTime? selectedDate;

  CycleTrackerPainter({
    required this.cycleDays,
    required this.periodDays,
    required this.currentDay,
    required this.ovulationDays,
    required this.ovulationDayStart,
    required this.textStyle,
    required this.outerCircleColor,
    required this.periodArcColor,
    required this.ovulationArcColor,
    required this.innerCircleColor,
    required this.pointerColor,
    this.cycleInfo,
    this.selectedDate,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;

    // Validate input values to prevent NaN errors
    if (cycleDays <= 0 || currentDay <= 0 || periodDays <= 0) {
      // Draw a simple placeholder circle if data is invalid
      final placeholderPaint = Paint()
        ..color = outerCircleColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 20;
      canvas.drawCircle(center, radius, placeholderPaint);

      // Draw inner circle
      final innerPaint = Paint()
        ..color = innerCircleColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, radius - 27, innerPaint);

      // Draw placeholder text
      final placeholderText = TextPainter(
        text: TextSpan(
          text: 'No cycle data',
          style: textStyle.copyWith(fontSize: 16, color: Colors.white),
        ),
        textAlign: TextAlign.center,
        textDirection: ui.TextDirection.ltr,
      );
      placeholderText.layout();
      placeholderText.paint(
        canvas,
        Offset(center.dx - placeholderText.width / 2,
            center.dy - placeholderText.height / 2),
      );
      return;
    }

    // Draw shadow for the outer circle - box-shadow: 0px 4px 4px 0px #00000040
    final shadowPaint = Paint()
      ..color = const Color(0x40000000) // #00000040
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4.0);
    canvas.drawCircle(center.translate(0, 4), radius + 9, shadowPaint);

    // Draw outer circle
    final outerPaint = Paint()
      ..color = outerCircleColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 20;
    canvas.drawCircle(center, radius, outerPaint);

    // Draw curvy period arc
    final periodPaint = Paint()
      ..color = periodArcColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 20
      ..strokeCap = StrokeCap.round;

    // Use actual period length from cycle info if available
    final actualPeriodDays =
        cycleInfo?.isPeriodDay == true ? periodDays : periodDays;
    final periodAngle = 2 * pi * (actualPeriodDays / cycleDays);
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      periodAngle,
      false,
      periodPaint,
    );

    // Draw ovulation arc - show 5-day fertile window
    if (cycleInfo?.isOvulationDay == true || ovulationDayStart > 0) {
      final ovulationPaint = Paint()
        ..color = ovulationArcColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 20
        ..strokeCap = StrokeCap.round;

      // Use the calculated ovulation start (already accounts for 5-day window)
      final ovulationStartAngle =
          2 * pi * ((ovulationDayStart - 1) / cycleDays) - pi / 2;
      final ovulationAngle = 2 * pi * (ovulationDays / cycleDays);
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        ovulationStartAngle,
        ovulationAngle,
        false,
        ovulationPaint,
      );
    }

    // Draw day numbers on the arc
    final textPainter = TextPainter(textDirection: ui.TextDirection.ltr);

    for (int i = 1; i <= periodDays; i++) {
      final angle = 2 * pi * ((i - 0.5) / cycleDays) - pi / 2;
      final textCenter = Offset(
        center.dx + radius * cos(angle),
        center.dy + radius * sin(angle),
      );

      // Special styling for day 1 if it's the current day
      final isCurrentDay = (currentDay == i);
      final numberStyle = textStyle.copyWith(
        fontSize: 14,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      );

      // Draw a blue circle background for day 1 if it's the current day
      if (isCurrentDay && i == 1) {
        final circlePaint = Paint()
          ..color = const Color(0xFF4A90E2)
          ..style = PaintingStyle.fill;
        canvas.drawCircle(textCenter, 12, circlePaint);
      }

      textPainter.text = TextSpan(text: i.toString(), style: numberStyle);
      textPainter.layout();
      textPainter.paint(
        canvas,
        textCenter - Offset(textPainter.width / 2, textPainter.height / 2),
      );
    }

    // Draw circular container (inner circle) with shadow
    final containerPaint2 = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final innerRadius2 = radius - 10;

    // Draw the inner circle
    canvas.drawCircle(center, innerRadius2, containerPaint2);

    // Draw circular container (inner circle) with shadow
    final containerPaint = Paint()
      ..color = innerCircleColor
      ..style = PaintingStyle.fill;

    final innerRadius = radius - 27;

    // Create a path for the inner circle
    final innerCirclePath = Path()
      ..addOval(Rect.fromCircle(center: center, radius: innerRadius));

    // Draw shadow for the inner circle (make it more prominent)
    canvas.drawShadow(
        innerCirclePath, Colors.black.withValues(alpha: 0.4), 16.0, true);

    // Draw the inner circle
    canvas.drawCircle(center, innerRadius, containerPaint);

    // Draw the current day indicator text using cycleInfo if available
    String dateText = '';
    String cycleText = '';
    String phaseText = '';

    // Format the date if selectedDate is provided
    if (selectedDate != null) {
      //today's date is before was
  dateText = DateFormat('MMMM d yyyy').format(selectedDate!) + (DateTime.now().isBefore(selectedDate!) ? ' is' : ' was');
    }

    if (cycleInfo != null) {
      // Use calculated cycle information
      if (cycleInfo!.isPeriodDay) {
        cycleText = 'Day ${cycleInfo!.cycleDay}\nof your period';
      } else if (cycleInfo!.isOvulationDay) {
        cycleText = 'Day ${cycleInfo!.cycleDay}\nof your cycle';
      } else {
        cycleText = 'Day ${cycleInfo!.cycleDay}\nof your cycle';
      }

      // Add phase information
      phaseText = '${cycleInfo!.cyclePhase}';
    } else {
      // Fallback to old logic
      cycleText = currentDay <= periodDays
          ? 'Day $currentDay\nof your period'
          : currentDay >= 14 && currentDay <= 14 + 7
              ? 'Day ${currentDay - 13}\nof ovulation'
              : 'Day $currentDay\nof your cycle';
    }

    // Draw date text if available
    if (dateText.isNotEmpty) {
      final dateTextPainter = TextPainter(
        text: TextSpan(
          text: dateText,
          style: textStyle.copyWith(
            fontSize: 13,
            fontWeight: FontWeight.w400,
            color: Colors.white,
          ),
        ),
        textAlign: TextAlign.center,
        textDirection: ui.TextDirection.ltr,
      );
      dateTextPainter.layout();
      dateTextPainter.paint(
        canvas,
        Offset(center.dx - dateTextPainter.width / 2,
            center.dy - dateTextPainter.height / 2 - 50),
      );
    }

    // Draw cycle day text
    final currentDayText = TextPainter(
      text: TextSpan(
        text: cycleText,
        style: textStyle.copyWith(
          fontSize: 20,
          fontWeight: FontWeight.w700,
          color: Colors.white,
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.ltr,
    );
    currentDayText.layout();

    // Position cycle text based on whether date text and phase text are present
    double cycleTextYOffset = 0;
    if (dateText.isNotEmpty && phaseText.isNotEmpty) {
      cycleTextYOffset =
          -5; // Move up slightly when both date and phase are present
    } else if (dateText.isNotEmpty) {
      cycleTextYOffset = 10; // Move down when only date is present
    } else if (phaseText.isNotEmpty) {
      cycleTextYOffset = -10; // Move up when only phase is present
    }

    final cycleTextOffset = Offset(
        center.dx - currentDayText.width / 2,
        center.dy -
            currentDayText.height / 2 +
            cycleTextYOffset +
            (dateText.isNotEmpty ? 8 : 0));

    currentDayText.paint(canvas, cycleTextOffset);

    // Draw phase text if available
    if (phaseText.isNotEmpty) {
      final phaseTextPainter = TextPainter(
        text: TextSpan(
          text: phaseText,
          style: textStyle.copyWith(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.white, // Use purple color for text
          ),
        ),
        textAlign: TextAlign.center,
        textDirection: ui.TextDirection.ltr,
      );
      phaseTextPainter.layout();

      // Position phase text below the cycle text
      final phaseTextOffset = Offset(
        center.dx - phaseTextPainter.width / 2,
        cycleTextOffset.dy + currentDayText.height + 20,
      );

      // Draw pill-shaped background
      final pillPadding = 8.0;
      final pillWidth = phaseTextPainter.width + (pillPadding * 2);
      final pillHeight = phaseTextPainter.height + (pillPadding);
      final pillRadius = pillHeight / 2;

      final pillRect = RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(
              center.dx, phaseTextOffset.dy + phaseTextPainter.height / 2),
          width: pillWidth,
          height: pillHeight,
        ),
        Radius.circular(pillRadius),
      );

      final pillPaint = Paint()
        ..color = Colors.white.withValues(alpha: 0.2)
        ..style = PaintingStyle.fill;

      canvas.drawRRect(pillRect, pillPaint);

      // Draw the phase text on top of the pill background
      phaseTextPainter.paint(canvas, phaseTextOffset);
    }

    // Draw triangle pointer (extending from inner circle to outer ring)
    final pointerPaint = Paint()
      ..color = innerCircleColor // Use the same color as inner circle (purple)
      ..style = PaintingStyle.fill;

    // Calculate the angle for the current day - align with the day numbers
    final currentDayAngle = 2 * pi * ((currentDay - 0.5) / cycleDays) - pi / 2;

    // Position the pointer to extend from inner circle edge to just before outer ring
    final innerPointerRadius = innerRadius; // Start from inner circle edge
    final outerPointerRadius = radius - 12; // End just before the outer ring

    // Create a more precise triangular pointer
    final trianglePath = Path()
      ..moveTo(
        center.dx +
            outerPointerRadius *
                cos(currentDayAngle), // Tip pointing to current day
        center.dy + outerPointerRadius * sin(currentDayAngle),
      )
      ..lineTo(
        center.dx +
            innerPointerRadius * cos(currentDayAngle - 0.08), // Left base
        center.dy + innerPointerRadius * sin(currentDayAngle - 0.08),
      )
      ..lineTo(
        center.dx +
            innerPointerRadius * cos(currentDayAngle + 0.08), // Right base
        center.dy + innerPointerRadius * sin(currentDayAngle + 0.08),
      )
      ..close();

    // Draw shadow for the pointer
    final shadowPath = Path()
      ..moveTo(
        center.dx + outerPointerRadius * cos(currentDayAngle),
        center.dy + outerPointerRadius * sin(currentDayAngle),
      )
      ..lineTo(
        center.dx + innerPointerRadius * cos(currentDayAngle - 0.08),
        center.dy + innerPointerRadius * sin(currentDayAngle - 0.08),
      )
      ..lineTo(
        center.dx + innerPointerRadius * cos(currentDayAngle + 0.08),
        center.dy + innerPointerRadius * sin(currentDayAngle + 0.08),
      )
      ..close();

    // Draw shadow
    canvas.drawShadow(
        shadowPath, Colors.black.withValues(alpha: 0.3), 8.0, true);

    // Draw the pointer
    canvas.drawPath(trianglePath, pointerPaint);
  }

  @override
  bool shouldRepaint(covariant CycleTrackerPainter oldDelegate) {
    return oldDelegate.cycleDays != cycleDays ||
        oldDelegate.periodDays != periodDays ||
        oldDelegate.currentDay != currentDay ||
        oldDelegate.ovulationDays != ovulationDays ||
        oldDelegate.ovulationDayStart != ovulationDayStart ||
        oldDelegate.outerCircleColor != outerCircleColor ||
        oldDelegate.periodArcColor != periodArcColor ||
        oldDelegate.ovulationArcColor != ovulationArcColor ||
        oldDelegate.innerCircleColor != innerCircleColor ||
        oldDelegate.pointerColor != pointerColor ||
        oldDelegate.selectedDate != selectedDate;
  }
}
