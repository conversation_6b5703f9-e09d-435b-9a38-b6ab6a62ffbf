import 'package:auto_route/auto_route.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Page,Route')
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
        /// routes go here
        AutoRoute(page: SplashRoute.page, initial: true),
        AutoRoute(page: WelcomeRoute.page),
        AutoRoute(page: LoginRoute.page),
        AutoRoute(page: SettingsRoute.page),
        //AutoRoute(page: DeviceSettingsRoute.page),
        AutoRoute(page: HomeRoute.page),
        AutoRoute(page: PairDeviceRoute.page),
        AutoRoute(page: SignUpRoute.page),
        AutoRoute(page: ResetPasswordRoute.page),
        AutoRoute(page: TroubleshootingRoute.page),
        AutoRoute(page: MedicationRoute.page),
        AutoRoute(page: HelpCenterHomeRoute.page),
        CustomRoute(
          page: NotificationsRoute.page,
          transitionsBuilder: TransitionsBuilders.slideLeft,
          durationInMilliseconds: 200,
          reverseDurationInMilliseconds: 300,
        ),
        AutoRoute(page: ProfilePictureRoute.page),
        AutoRoute(page: ProfileRoute.page),
        AutoRoute(page: GetStartedRoute.page),
        AutoRoute(page: RemoteOneRoute.page),
        AutoRoute(page: EmailVerificationRoute.page),
        AutoRoute(page: ExtendedCalenderRoute.page),
        // AutoRoute(page: PeriodTrackingCalendarRoute.page),
        AutoRoute(page: PeriodTrackingEditRoute.page),
        AutoRoute(page: PeriodTrackingViewRoute.page),
        AutoRoute(page: DashboardRoute.page),
        AutoRoute(page: MedicationCabinetRoute.page),
        AutoRoute(page: ProductShowcaseRoute.page),
        AutoRoute(page: OnboardingStartScreen.page),
        AutoRoute(page: TherapyAnalyticsChartRoute.page),
        AutoRoute(page: VirtualRemoteRoute.page),
        AutoRoute(page: DailySymptomTrackingRoute.page),
      ];
}
